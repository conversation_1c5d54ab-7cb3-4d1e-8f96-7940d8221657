@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.Models.Blocks;
@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	Layout = "childPageLayout.cshtml";

	var contents = Model.Children()
	.Where(x => x.IsVisible())
	.OrderBy(x => x.SortOrder)
	.ToList();
}

<div class="listing-documents-layout">
	<div class="container">
		<div class="timeline">
			@if (contents != null && contents.Any())
			{
				@foreach (var item in contents)
				{
					var title = item.Value<string>("title");
					var file = item.Value<IPublishedContent>("file")?.Url() ?? "";
					var date = item.Value<DateTime>("publishedDate").ToString("dd");
					var yearMonth = item.Value<DateTime>("publishedDate").ToString("yyyy-MM");
					
					<div class="timeline-item">
						<div class="timeline-date">
							<div class="day">@date</div>
							<div class="month">@yearMonth</div>
						</div>
						<div class="timeline-content">
							<a href="@file" target="_blank">
								@title
							</a>
						</div>
						<a href="@file" class="timeline-link" target="_blank">
							<svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">
								<circle cx="17" cy="17" r="17" fill="#1B73C0"/>
								<path d="M10.5193 22.4612C9.99571 21.9415 9.94319 21.4708 10.3618 21.049L19.1524 12.1919C19.571 11.7702 20.0421 11.8191 20.5657 12.3388C21.0894 12.8586 21.1419 13.3293 20.7233 13.7511L11.9327 22.6081C11.5141 23.0299 11.043 22.9809 10.5193 22.4612Z" fill="white"/>
								<path d="M20.2707 12.7705L12.5059 12.7997C12.359 12.8028 12.2129 12.7766 12.0762 12.7227C11.9396 12.6688 11.815 12.5881 11.7098 12.4855C11.6046 12.3829 11.5209 12.2604 11.4636 12.1251C11.4062 11.9898 11.3764 11.8444 11.3759 11.6975C11.3753 11.5505 11.404 11.405 11.4603 11.2692C11.5167 11.1335 11.5994 11.0104 11.7038 10.907C11.8083 10.8036 11.9322 10.722 12.0685 10.667C12.2048 10.6121 12.3506 10.5848 12.4976 10.5868L21.3509 10.5534C21.4979 10.5504 21.644 10.5767 21.7807 10.6308C21.9174 10.6849 22.042 10.7656 22.1472 10.8684C22.2523 10.9712 22.3359 11.0938 22.3931 11.2293C22.4503 11.3647 22.48 11.5102 22.4803 11.6572L22.5125 20.5102C22.5136 20.8037 22.3982 21.0856 22.1914 21.294C21.9847 21.5023 21.7037 21.62 21.4102 21.6212C21.1167 21.6223 20.8347 21.5069 20.6264 21.3001C20.418 21.0934 20.3003 20.8124 20.2992 20.5189L20.27 12.7705L20.2707 12.7705Z" fill="white"/>
							</svg>
						</a>
					</div>
				}
			}
		</div>
	</div>
</div>