@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Web.Common.PublishedModels
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage

@{
    Layout = "childPageLayout.cshtml";

    var contents = Model.Children()
        .Where(x => x.IsVisible())
        .OrderByDescending(x => x.Value<DateTime>("publishedDate"))
        .ToList();

    var years = contents
        .Where(x => x.HasValue("publishedDate"))
        .Select(x => x.Value<DateTime>("publishedDate").Year)
        .Distinct()
        .OrderByDescending(y => y)
        .ToList();
}

<div class="company-presentations-layout">
    <div class="container">
        <div class="timeline-header">
			<select id="yearFilter">
				<option value="">All Years</option>
				@foreach (var y in years)
				{
					<option value="@y">@y</option>
				}
			</select>
		</div>

        <div class="timeline" id="timelineContainer">
            @foreach (var item in contents)
            {
                var title = item.Value<string>("title");
                var file = item.Value<IPublishedContent>("file")?.Url() ?? "";
                var date = item.Value<DateTime>("publishedDate").ToString("dd");
                var yearMonth = item.Value<DateTime>("publishedDate").ToString("yyyy-MM");
                var year = item.Value<DateTime>("publishedDate").ToString("yyyy");;

                <div class="timeline-item" data-year="@year">
                    <div class="timeline-date">
                        <div class="day">@date</div>
                        <div class="month">@yearMonth</div>
                    </div>
                    <div class="timeline-content">
                        <a href="@file" target="_blank">@title</a>
                    </div>
                    <a href="@file" class="timeline-link" target="_blank">
                        <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">
                            <circle cx="17" cy="17" r="17" fill="#1B73C0"/>
                            <path d="M10.5 22.4L19.1 12.2C19.5 11.8 20 11.8 20.6 12.3C21.1 12.8 21.1 13.3 20.7 13.8L12 22.6C11.5 23 11 23 10.5 22.4Z" fill="white"/>
                            <path d="M20.3 12.7H12.5C11.9 12.7 11.4 12.3 11.4 11.7C11.4 11.1 11.9 10.6 12.5 10.6H21.4C22 10.6 22.5 11.1 22.5 11.7V20.5C22.5 21.1 22 21.6 21.4 21.6C20.8 21.6 20.3 21.1 20.3 20.5V12.7Z" fill="white"/>
                        </svg>
                    </a>
                </div>
            }
        </div>

        <div class="pagination">
            <button id="prevPage">Prev</button>
            <span id="pageInfo"></span>
            <button id="nextPage">Next</button>
        </div>
    </div>
</div>

<script>
	document.addEventListener("DOMContentLoaded", function() {
		console.log("Company presentations page loaded");

		const yearFilter = document.getElementById("yearFilter");
		const timelineItems = document.querySelectorAll(".timeline-item");

		console.log("Year filter element:", yearFilter);
		console.log("Timeline items count:", timelineItems.length);

		if (yearFilter) {
			console.log("Year filter options:", yearFilter.options.length);
			for (let i = 0; i < yearFilter.options.length; i++) {
				console.log("Option", i, ":", yearFilter.options[i].value, yearFilter.options[i].text);
			}
		}

		if (timelineItems.length > 0) {
			timelineItems.forEach((item, index) => {
				console.log("Item", index, "data-year:", item.dataset.year);
			});
		}
	});
</script>
