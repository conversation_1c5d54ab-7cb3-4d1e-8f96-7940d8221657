@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models

@{
    Layout = "masterLayout.cshtml";
    var home = Model.Root();
    var title = "";
    if (Model.HasValue("pageTitle") && Model.Value("pageTitle") != null)
    {
        title = Model.Value("pageTitle").ToString();
    }
    var templateID = Model.TemplateId.Value;
    var documentType = Model.ContentType.Alias;

}

<div
    class="childpage-content bg-white template-@templateID <EMAIL>  @Model.Name.ToLower().Replace(" ", "-").Replace("'", "-")">
    <div class="fluid-container">
        @* @if (documentType != "errorPage" && documentType != "internalServerError")
        {
            <section class="childpage-banner banner">
                @* <partial name="Layout/Banner" />
            </section>
        } *@
        <div class="inner-content">
            @* <div class="page-title-box">
                <div class="container">
                    <div class="title">@title</div>
                </div>
            </div> *@
            @RenderBody()
        </div>
    </div>
</div>
