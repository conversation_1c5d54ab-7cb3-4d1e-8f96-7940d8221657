$(document).ready(function () {
  //add infor to last item of breadcrumb
  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const tab = urlParams.get("tab");
  var currentUrl = $(location).attr("href");
  // $('.last-item a').attr("href", currentUrl)
  if (tab === "" || typeof tab == "undefined" || tab == null) {
    var firstNavLink = $(".nav-link").first().text();
    $(".last-item a").text(firstNavLink);
  } else {
    let newText = $("#" + tab).text();
    $(".last-item a").text(newText);
    // $('.last-item').addClass('active');
  }
  $(".nav-item .nav-link").click(function (e) {
    e.preventDefault();
    $(".nav-item .nav-link").removeClass("active");
    $(this).addClass("active");
    let pageItemID = $(this).attr("id");
    $(".tab-pane").removeClass("active in show");
    $("#" + pageItemID + "-content").addClass("active in show");

    var url = window.location.href;
    var hashIndex = url.indexOf("#");
    if (hashIndex !== -1) {
      var hashSubstring = url.substring(hashIndex + 1);
    }

    var $parent = $("#" + hashSubstring + "-content");
    var $bannerChild = $parent.find(".banner-child");

    var $valueTitlePage = $bannerChild.find(".first-line").html();
    var $valueParentPage = $bannerChild.find(".content-description p").html();
    var dataBG = $bannerChild.find(".swiper-slide").attr("data-bg");

    $(".banner-parent")
      .find(".swiper-slide")
      .find(".first-line")
      .html($valueTitlePage);
    $(".banner-parent")
      .find(".swiper-slide")
      .find(".content-description p")
      .html($valueParentPage);
    $(".banner-parent")
      .find(".swiper-slide")
      .css("background", "url(" + dataBG + ")");

    //active breadcrumb when click nav item
    // $('.breadcrumb-nav-item').removeClass('active')
    // $('.last-item').addClass('active');

    //change infor to last item of breadcrumd
    let newText = $("#" + pageItemID).text();
    $(".last-item a").text(newText);
  });

  $(".menu-item.level-3 a").click(function (e) {
    var url = $(this).attr("href");
    var hashIndex = url.indexOf("#");
    if (hashIndex !== -1) {
      var hashSubstring = url.substring(hashIndex + 1);
    }

    var $parent = $("#" + hashSubstring + "-content");
    var $bannerChild = $parent.find(".banner-child");

    var $valueTitlePage = $bannerChild.find(".first-line").html();
    var $valueParentPage = $bannerChild.find(".content-description p").html();
    var dataBG = $bannerChild.find(".swiper-slide").attr("data-bg");

    $(".banner-parent")
      .find(".swiper-slide")
      .find(".first-line")
      .html($valueTitlePage);
    $(".banner-parent")
      .find(".swiper-slide")
      .find(".content-description p")
      .html($valueParentPage);
    $(".banner-parent")
      .find(".swiper-slide")
      .css("background", "url(" + dataBG + ")");

    $(".nav-item .nav-link").removeClass("active");
    $("#" + hashSubstring).addClass("active");
    $(".tab-pane").removeClass("active in show");
    $("#" + hashSubstring + "-content").addClass("active in show");

    //change infor to last item of breadcrumd
    let newText = $("#" + hashSubstring).text();
    $(".last-item a").text(newText);
  });
});
