document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll(".footer-wechat .icon").forEach(function (icon) {
    icon.addEventListener("click", function () {
      const parent = this.closest(".footer-wechat");
      if (parent) {
        parent.classList.toggle("close");
      }
    });
  });

  document
    .querySelectorAll(
      ".site-footer .menu-item.hassub > a, .site-footer .menu-item.hassub .open-sub"
    )
    .forEach(function (link) {
      link.addEventListener("click", function (e) {
        e.preventDefault();
        e.stopPropagation();
        const menuItem = this.closest(".site-footer .menu-item.hassub");
        if (menuItem) {
          menuItem.classList.toggle("open");
        }
      });
    });
});
