﻿@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Web.Common.PublishedModels;
@using Microsoft.AspNetCore.Html;

@{
    Layout = "childPageLayout.cshtml";
	var toolSource = Model.Value("toolSource");
    var additionalText = Model.Value("additionalText");
    var pageTitle = Model.Value("pageTitle").ToString();

    var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
    language = language.Equals("en-US") ? "en-GB" :
    language.Equals("zh-Hans") ? "zh-CN" :
    language.Equals("zh-Hant") ? "zh-TW" :
    language;
    var dataToolName = Model.Value<string>("dataToolName");
    var dataToolVersion = Model.Value<string>("dataToolVersion");
    var dataToolCompany = Model.Value<string>("dataToolCompany");
    var dataToolUrlBase = Model.Value<string>("dataToolUrlBase");
    var dataLazy = Model.Value<string>("dataLazy");
    var dataToolEnforcedCompanyCode = Model.Value<string>("dataToolEnforcedCompanyCode");
    var dataToolLanguage = Model.Value<string>("dataToolLanguage");
    var dataToolAbsoluteUrl = Model.Value<string>("dataToolAbsoluteUrl");

}
<div class="container">
    <div class="page-title">
		@pageTitle
	</div>
    @if (dataToolName.Equals("Bundle"))
    {
        <euroland-tool data-tool-name="@dataToolName" data-tool-version="@dataToolVersion"
            data-tool-absolute-url="@dataToolAbsoluteUrl" data-tool-language="@dataToolLanguage"
            data-tool-company="@dataToolCompany">
        </euroland-tool>
    }    
    else
    {
        <euroland-tool data-tool-name="@dataToolName" data-tool-company="@dataToolCompany"
            data-tool-url-base="@dataToolUrlBase" data-tool-language="@dataToolLanguage"
            data-tool-version="@dataToolVersion">
        </euroland-tool>
    }

    @if (Model.HasValue("additionalText"))
    {
        <div class="additional-text">
            <p>
                @additionalText
            </p>
        </div>
    }
</div>
