.site-header {
  position: fixed;
  top: 0;
  width: 100%;
  left: 0;
  right: 0;
  z-index: 9999;
  transition: all 0.3s linear;
  &.header-fixed {
    box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px,
      rgba(0, 0, 0, 0.15) 0px 1px 3px 1px;
  }
  .header-top {
    background-color: #fff;
  }
  .menu-item.level-4,
  .menu-item.level-5 {
    display: none;
  }
  .menu-item.menu-Home-page {
    display: none;
    @media screen and (max-width: 991px) {
      display: block;
    }
  }
  .hidden {
    display: none;
  }

  .wrap-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    @media screen and (min-width: 992px) {
      height: 100px;
    }
    .logo {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      img {
        max-width: 36rem;
        width: 100%;
        height: auto;
        object-fit: cover;
      }
      @media screen and (max-width: 1455px) {
        img {
          max-width: 30rem;
        }
      }
      @media screen and (max-width: 1199px) {
        img {
          max-width: 25rem;
        }
      }
      @media screen and (max-width: 767px) {
        img {
          max-width: 20rem;
        }
      }
    }
    .wrap-right-header {
      flex: 1 0 0%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .menu-pc.nav-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 4.6rem;
      @media screen and (max-width: 1499px) {
        gap: 4rem;
      }
      @media screen and (max-width: 1199px) {
        gap: 3rem;
      }
      @media screen and (max-width: 991px) {
        display: none;
      }

      .menu-item.level-2 {
        position: relative;
        a {
          color: #000;
          font-size: 1.8rem;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          padding: 20px 0;
          transition: all 0.3s ease;
        }
        &.active {
        }

        &:hover {
          .wrap-sub-menu {
            display: block;
          }
          a {
            opacity: 1;
          }
        }
        .wrap-sub-menu {
          background-color: #fff;
          box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
          display: none;
          left: 50%;
          margin: 0;
          padding: 1rem 0;
          position: absolute;
          top: 62px;
          transform: translateX(-50%);
          transition: all 0.2s ease;
          width: 241px;
          z-index: 10;
          padding: 0 12px;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 9px;
            background: url("/media/5bpdstlr/vector01.png") no-repeat center
              center / cover;
          }

          &::after {
            content: " ";
            background: transparent;
            height: 50px;
            left: 50%;
            position: absolute;
            top: -40px;
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
            width: 100%;
            z-index: 100;
          }
          .menu-sub-lv2 {
            list-style: none;
            display: flex;
            flex-direction: column;
            .menu-item.level-3 {
              &:not(:last-child) {
                border-bottom: 1px solid #eeeeee;
              }
              a {
                display: inline-block;
                width: 100%;
                color: #333;
                font-size: 1.8rem;
                font-style: normal;
                font-weight: 400;
                line-height: 2;
                padding: 7px 0;
                transition: all 0.3s ease-in-out;
                &:hover {
                  // color: $color-primary;
                  background: linear-gradient(90deg, #d71820 0%, #710d11 86.5%);
                  background-clip: text;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
              .wrap-sub-menu {
                display: none;
              }
            }
          }
        }
      }
    }
    .language {
      margin-left: 4.6rem;
      @media screen and (max-width: 1499px) {
        margin-left: 4rem;
      }
      @media screen and (max-width: 1199px) {
        margin-left: 3rem;
      }

      .lang-status {
        position: relative;
        align-items: center;
        justify-content: center;
        position: relative;
        a {
          color: #000;
          font-size: 1.8rem;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          position: relative;
          &:not(:last-child) {
            margin-right: 15px;
            &::after {
              content: "";
              position: absolute;
              right: -7.5px;
              top: 50%;
              transform: translateY(-50%);
              width: 2px;
              height: 15px;
              background-color: #000;
            }
          }
          &.active {
            color: $color-primary;
          }
          &:hover {
            color: $color-primary;
          }
        }
      }
    }

    .off-canvas {
      display: none;

      @media (max-width: 991px) {
        display: block;
        margin-left: 20px;
      }
    }
  }

  .header-middle {
    display: none;
    background-color: #fff;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    height: calc(110vh - 80px);
    padding: 0 0 3.2rem;
    opacity: 0;
    overflow-y: auto;
    position: fixed;
    right: -100vw;
    top: 80px;
    transition: all 0.5s;
    width: 100%;
    z-index: 99999;
    border-top: 1px solid #eee;
    &.open {
      opacity: 1;
      right: 0;
    }
    @media screen and (min-width: 768px) {
      top: 60px;
    }
    @media screen and (max-width: 991px) {
      display: block;
    }
    .menu-mobile {
      .wrap-menu-mobile {
        .menu-responsive.nav-container {
          // display: none;
          padding: 0;
          width: 100%;
          list-style: none;
          background-color: #fff;
          position: relative;
          z-index: 10;
          ul {
            float: none;
            height: auto;
          }
          .menu-item.level-2 {
            position: relative;
            transition: all 0.3s ease;
            border-bottom: 1px solid #eee;
            a {
              transition: all 0.3s ease;
              font-size: 16px;
              color: #333;
              display: block;
              width: fit-content;
              padding: 1.6rem;
              &:hover {
                // color: $color-primary;
                background: linear-gradient(90deg, #d71820 0%, #710d11 86.5%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
          }
          .menu-item.level-2.hassub {
            .open-sub {
              background: url("/media/vf5dcvfb/arrow-icon.png") center no-repeat;
              background-size: contain;
              display: block;
              height: 15px;
              position: absolute;
              right: 1.6rem;
              top: 22px;
              transition: all 0.3s;
              width: 15px;
              transform: rotate(-180deg);
            }
            &.close {
              .menu-responsive.menu-sub-lv2 {
                display: none;
              }
              .open-sub {
                transform: rotate(0);
              }
            }
            &.open {
            }
          }
        }
        .menu-responsive.menu-sub-lv2 {
          padding-left: 1.6rem;
          .menu-item.level-3 {
            position: relative;
            transition: all 0.3s ease;
            border-top: 1px solid hsla(0, 0%, 93%, 0.933);
            a {
              transition: all 0.3s ease;
              font-size: 16px;
              color: #737373;
              display: block;
              padding: 1.6rem;
              &:hover {
                a {
                  color: #e8380d;
                }
              }
            }
          }
        }
      }
    }
  }
}

.menuicon-label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 28px;
  height: 18px;

  &:after,
  &:before {
    content: "";
  }

  span,
  &:after,
  &:before {
    display: block;
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 2px;
    transform: translateY(-50%);
    background: #000;
    transition: all 0.36s;
  }

  span {
    .active-menu & {
      opacity: 0;
    }
  }

  &::before {
    margin-top: -8px;

    .active-menu & {
      transform: translateY(-50%) rotate(45deg);
      margin-top: 0;
    }
  }

  &::after {
    margin-top: 8px;

    .active-menu & {
      transform: translateY(-50%) rotate(-45deg);
      margin-top: 0;
    }
  }
}
