
// Company Presentations functionality
(function() {
    'use strict';

    let allItems = [];
    let filteredItems = [];
    let currentPage = 1;
    const itemsPerPage = 6;

    function initializeCompanyPresentations() {
        if (!document.querySelector('.company-presentations-layout')) {
            return;
        }

        allItems = Array.from(document.querySelectorAll(".timeline-item"));
        filteredItems = [...allItems];

        console.log("Initializing company presentations with", allItems.length, "items");

        setupEventListeners();
        renderPage();
    }

    function renderPage() {
        // Ẩn tất cả items
        allItems.forEach(item => item.style.display = "none");

        const pageInfoElement = document.getElementById("pageInfo");
        if (!pageInfoElement) return;

        if (filteredItems.length === 0) {
            pageInfoElement.innerHTML = "No items found";
            return;
        }

        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
        if (currentPage > totalPages) currentPage = totalPages;
        if (currentPage < 1) currentPage = 1;

        const start = (currentPage - 1) * itemsPerPage;
        const end = start + itemsPerPage;

        // Hiển thị items cho trang hiện tại
        filteredItems.slice(start, end).forEach(item => {
            item.style.display = "flex";
        });

        // Tạo pagination với số trang
        renderPagination(totalPages);

        // Cập nhật trạng thái buttons
        const prevButton = document.getElementById("prevPage");
        const nextButton = document.getElementById("nextPage");

        if (prevButton) prevButton.disabled = currentPage === 1;
        if (nextButton) nextButton.disabled = currentPage === totalPages;
    }

    function renderPagination(totalPages) {
        const pageInfoElement = document.getElementById("pageInfo");
        if (!pageInfoElement) return;

        let paginationHTML = '';

        // Nếu chỉ có 1 trang thì không hiển thị pagination numbers
        if (totalPages <= 1) {
            pageInfoElement.innerHTML = '';
            return;
        }

        // Xác định số trang hiển thị tối đa (responsive)
        const isMobile = window.innerWidth <= 768;
        const maxVisiblePages = isMobile ? 3 : 5;

        if (totalPages <= maxVisiblePages) {
            // Nếu tổng số trang ít hơn hoặc bằng maxVisiblePages, hiển thị tất cả
            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `<span class="page-number ${currentPage === i ? 'active' : ''}" data-page="${i}">${i}</span>`;
            }
        } else {
            // Logic phức tạp hơn cho nhiều trang
            let startPage, endPage;

            if (currentPage <= Math.ceil(maxVisiblePages / 2)) {
                // Gần đầu
                startPage = 1;
                endPage = maxVisiblePages - 1;
            } else if (currentPage >= totalPages - Math.floor(maxVisiblePages / 2)) {
                // Gần cuối
                startPage = totalPages - maxVisiblePages + 2;
                endPage = totalPages;
            } else {
                // Ở giữa
                startPage = currentPage - Math.floor((maxVisiblePages - 3) / 2);
                endPage = currentPage + Math.floor((maxVisiblePages - 3) / 2);
            }

            // Luôn hiển thị trang 1
            paginationHTML += `<span class="page-number ${currentPage === 1 ? 'active' : ''}" data-page="1">1</span>`;

            // Thêm ... nếu cần
            if (startPage > 2) {
                paginationHTML += '<span class="page-ellipsis">...</span>';
            }

            // Hiển thị các trang ở giữa
            for (let i = startPage; i <= endPage; i++) {
                if (i > 1 && i < totalPages) {
                    paginationHTML += `<span class="page-number ${currentPage === i ? 'active' : ''}" data-page="${i}">${i}</span>`;
                }
            }

            // Thêm ... nếu cần
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="page-ellipsis">...</span>';
            }

            // Luôn hiển thị trang cuối (nếu không phải trang 1)
            if (totalPages > 1) {
                paginationHTML += `<span class="page-number ${currentPage === totalPages ? 'active' : ''}" data-page="${totalPages}">${totalPages}</span>`;
            }
        }

        pageInfoElement.innerHTML = paginationHTML;

        // Thêm event listeners cho các số trang
        const pageNumbers = pageInfoElement.querySelectorAll('.page-number');
        pageNumbers.forEach(pageNumber => {
            pageNumber.addEventListener('click', function() {
                const targetPage = parseInt(this.dataset.page);
                if (targetPage !== currentPage && !isNaN(targetPage)) {
                    currentPage = targetPage;
                    renderPage();
                }
            });
        });
    }

    function filterByYear(year) {
        console.log("Filtering by year:", year);

        if (!year || year === "") {
            filteredItems = [...allItems];
        } else {
            filteredItems = allItems.filter(item => {
                const itemYear = item.dataset.year;
                return itemYear === year;
            });
        }

        console.log("Filtered items count:", filteredItems.length);
        currentPage = 1;
        renderPage();
    }

    function setupEventListeners() {
        // Pagination buttons
        const prevButton = document.getElementById("prevPage");
        const nextButton = document.getElementById("nextPage");

        if (prevButton) {
            prevButton.addEventListener("click", function () {
                if (currentPage > 1) {
                    currentPage--;
                    renderPage();
                }
            });
        }

        if (nextButton) {
            nextButton.addEventListener("click", function () {
                const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderPage();
                }
            });
        }

        // Year filter - multiple approaches to handle nice-select
        setupYearFilter();
    }

    function setupYearFilter() {
        const yearFilterElement = document.getElementById("yearFilter");
        if (!yearFilterElement) {
            console.log("Year filter element not found");
            return;
        }

        // Approach 1: Direct event listener
        yearFilterElement.addEventListener("change", function () {
            console.log("Direct change event:", this.value);
            filterByYear(this.value);
        });

        // Approach 2: jQuery if available (for nice-select)
        if (typeof $ !== 'undefined') {
            $(yearFilterElement).on('change', function() {
                console.log("jQuery change event:", $(this).val());
                filterByYear($(this).val());
            });
        }

        // Approach 3: MutationObserver to watch for nice-select changes
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        // Nice-select might have been applied
                        setTimeout(setupNiceSelectHandler, 100);
                    }
                });
            });

            observer.observe(yearFilterElement, { attributes: true });
        }

        // Initial setup for nice-select
        setTimeout(setupNiceSelectHandler, 500);
    }

    function setupNiceSelectHandler() {
        if (typeof $ !== 'undefined') {
            const niceSelectElement = $('.nice-select');
            if (niceSelectElement.length > 0) {
                console.log("Nice-select detected, setting up handler");
                niceSelectElement.off('change.companyPresentations').on('change.companyPresentations', function() {
                    const originalSelect = $(this).siblings('select');
                    if (originalSelect.attr('id') === 'yearFilter') {
                        console.log("Nice-select change event:", originalSelect.val());
                        filterByYear(originalSelect.val());
                    }
                });
            }
        }
    }

    // Window resize handler for responsive pagination
    function handleResize() {
        if (document.querySelector('.company-presentations-layout')) {
            renderPage(); // Re-render pagination with new responsive settings
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCompanyPresentations);
    } else {
        initializeCompanyPresentations();
    }

    // Also initialize when jQuery is ready (for nice-select)
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            setTimeout(initializeCompanyPresentations, 100);
        });
    }

    // Add resize listener
    window.addEventListener('resize', debounce(handleResize, 250));

    // Debounce function to limit resize events
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

})();
