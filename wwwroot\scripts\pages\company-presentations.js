
// Company Presentations functionality
(function() {
    'use strict';

    let allItems = [];
    let filteredItems = [];
    let currentPage = 1;
    const itemsPerPage = 6;

    function initializeCompanyPresentations() {
        // Ki<PERSON>m tra xem chúng ta có đang ở trang company presentations không
        if (!document.querySelector('.company-presentations-layout')) {
            return;
        }

        allItems = Array.from(document.querySelectorAll(".timeline-item"));
        filteredItems = [...allItems];

        console.log("Initializing company presentations with", allItems.length, "items");

        setupEventListeners();
        renderPage();
    }

    function renderPage() {
        // Ẩn tất cả items
        allItems.forEach(item => item.style.display = "none");

        const pageInfoElement = document.getElementById("pageInfo");
        if (!pageInfoElement) return;

        if (filteredItems.length === 0) {
            pageInfoElement.innerText = "No items found";
            return;
        }

        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
        if (currentPage > totalPages) currentPage = totalPages;
        if (currentPage < 1) currentPage = 1;

        const start = (currentPage - 1) * itemsPerPage;
        const end = start + itemsPerPage;

        // Hiển thị items cho trang hiện tại
        filteredItems.slice(start, end).forEach(item => {
            item.style.display = "flex";
        });

        // Cập nhật thông tin trang
        pageInfoElement.innerText = `Page ${currentPage} of ${totalPages}`;

        // Cập nhật trạng thái buttons
        const prevButton = document.getElementById("prevPage");
        const nextButton = document.getElementById("nextPage");

        if (prevButton) prevButton.disabled = currentPage === 1;
        if (nextButton) nextButton.disabled = currentPage === totalPages;
    }

    function filterByYear(year) {
        console.log("Filtering by year:", year);

        if (!year || year === "") {
            filteredItems = [...allItems];
        } else {
            filteredItems = allItems.filter(item => {
                const itemYear = item.dataset.year;
                return itemYear === year;
            });
        }

        console.log("Filtered items count:", filteredItems.length);
        currentPage = 1;
        renderPage();
    }

    function setupEventListeners() {
        // Pagination buttons
        const prevButton = document.getElementById("prevPage");
        const nextButton = document.getElementById("nextPage");

        if (prevButton) {
            prevButton.addEventListener("click", function () {
                if (currentPage > 1) {
                    currentPage--;
                    renderPage();
                }
            });
        }

        if (nextButton) {
            nextButton.addEventListener("click", function () {
                const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderPage();
                }
            });
        }

        // Year filter - multiple approaches to handle nice-select
        setupYearFilter();
    }

    function setupYearFilter() {
        const yearFilterElement = document.getElementById("yearFilter");
        if (!yearFilterElement) {
            console.log("Year filter element not found");
            return;
        }

        // Approach 1: Direct event listener
        yearFilterElement.addEventListener("change", function () {
            console.log("Direct change event:", this.value);
            filterByYear(this.value);
        });

        // Approach 2: jQuery if available (for nice-select)
        if (typeof $ !== 'undefined') {
            $(yearFilterElement).on('change', function() {
                console.log("jQuery change event:", $(this).val());
                filterByYear($(this).val());
            });
        }

        // Approach 3: MutationObserver to watch for nice-select changes
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        // Nice-select might have been applied
                        setTimeout(setupNiceSelectHandler, 100);
                    }
                });
            });

            observer.observe(yearFilterElement, { attributes: true });
        }

        // Initial setup for nice-select
        setTimeout(setupNiceSelectHandler, 500);
    }

    function setupNiceSelectHandler() {
        if (typeof $ !== 'undefined') {
            const niceSelectElement = $('.nice-select');
            if (niceSelectElement.length > 0) {
                console.log("Nice-select detected, setting up handler");
                niceSelectElement.off('change.companyPresentations').on('change.companyPresentations', function() {
                    const originalSelect = $(this).siblings('select');
                    if (originalSelect.attr('id') === 'yearFilter') {
                        console.log("Nice-select change event:", originalSelect.val());
                        filterByYear(originalSelect.val());
                    }
                });
            }
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCompanyPresentations);
    } else {
        initializeCompanyPresentations();
    }

    // Also initialize when jQuery is ready (for nice-select)
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            setTimeout(initializeCompanyPresentations, 100);
        });
    }

})();
