
document.addEventListener("DOMContentLoaded", function () {
    const allItems = Array.from(document.querySelectorAll(".timeline-item"));
    const itemsPerPage = 5;
    let currentPage = 1;
    let filteredItems = [...allItems];

    function renderPage() {
       
        allItems.forEach(item => item.style.display = "none");

        if (filteredItems.length === 0) {
            document.getElementById("pageInfo").innerText = "No items found";
            return;
        }

        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
        if (currentPage > totalPages) currentPage = totalPages;

        const start = (currentPage - 1) * itemsPerPage;
        const end = start + itemsPerPage;

        filteredItems.slice(start, end).forEach(item => {
            item.style.display = "flex";
        });

        document.getElementById("pageInfo").innerText =
            `Page ${currentPage} of ${totalPages}`;

        document.getElementById("prevPage").disabled = currentPage === 1;
        document.getElementById("nextPage").disabled = currentPage === totalPages;
    }

    function filterByYear(year) {
        if (!year) {
            filteredItems = [...allItems];
        } else {
            filteredItems = allItems.filter(item => item.dataset.year === year);
        }
        currentPage = 1;
        renderPage();
    }

    // sự kiện nút
    document.getElementById("prevPage").addEventListener("click", function () {
        if (currentPage > 1) {
            currentPage--;
            renderPage();
        }
    });

    document.getElementById("nextPage").addEventListener("click", function () {
        const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderPage();
        }
    });

    // sự kiện chọn năm
    document.getElementById("yearFilter").addEventListener("change", function () {
        filterByYear(this.value);
    });

    // khởi tạo
    renderPage();
});
