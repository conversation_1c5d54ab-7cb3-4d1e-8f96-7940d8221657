.error-page {
    text-align: center;

    .section {
        padding: 4rem 2rem;
    }

    .section .error {
        font-size: 150px;
        color: $color-primary;
    }

    .page {
        margin: 2rem 0;
        font-size: 20px;
        font-weight: 600;
        color: #444;
    }

    .back-home {
        display: inline-block;
        border: 2px solid #222;
        color: #222;
        text-transform: uppercase;
        font-weight: 600;
        padding: 0.75rem 1rem 0.6rem;
        transition: all 0.2s linear;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
        margin-top: 4rem;
    }

    .back-home:hover {
        background: #222;
        color: #ddd;
    }
}
.internal-server-error-page {
    height: 100vh;
    text-align: center;
    .error-section {
        padding: 40px 20px;
        h1 {
            font-size: 150px;
            color: $color-primary;
            text-shadow:
                1px 1px 1px #ddd,
                2px 2px 1px #ddd,
                3px 3px 1px #ddd,
                4px 4px 1px #ddd,
                5px 5px 1px #ddd,
                6px 6px 1px #ddd,
                7px 7px 1px #ddd,
                8px 8px 1px #ddd,
                25px 25px 8px rgba(0, 0, 0, 0.2);
            margin: 0;
        }
        h3 {
            margin: 20px 0;
            font-size: 30px;
            font-weight: 600;
            color: #000;
        }
        .error-content {
            color: #000;
            font-size: 18px;
            line-height: 1.5;
            font-weight: normal;
        }
    }
}
