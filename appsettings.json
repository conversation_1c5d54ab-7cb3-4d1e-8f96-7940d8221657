{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Error", "System": "Error"}}}, "ConnectionStrings": {"umbracoDbDSN": "server=bingo;database=umb-yunji-technology-ir;user id=test;password='123456';TrustServerCertificate=True;", "umbracoDbDSN_ProviderName": "Microsoft.Data.SqlClient"}, "GoogleRecaptcha": {"SiteKey": "SiteKey-value", "SecretKey": "SecretKey-value"}, "Umbraco": {"CMS": {"Global": {"Id": "341f50bb-3e68-4c20-be2f-18b57bf81764", "SanitizeTinyMce": true, "TimeOut": "00:59:00"}, "Hosting": {"LocalTempStorageLocation": "EnvironmentTemp"}, "Examine": {"LuceneDirectoryFactory": "TempFileSystemDirectoryFactory"}, "Content": {"AllowEditInvariantFromNonDefault": true, "Error404Collection": [{"Culture": "default", "ContentKey": "9ecf9b9d-28ab-4941-8d59-52ec471247fc"}], "Notifications": {"Email": "<EMAIL>"}, "ContentVersionCleanupPolicy": {"EnableCleanup": true}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"BackOfficeHost": "https://dev.vn.euroland.com:8083"}, "WebRouting": {"UmbracoApplicationUrl": "https://dev.vn.euroland.com:8083"}, "Logging": {"MaxLogAge": "2.00:00:00"}}, "Storage": {"AzureBlob": {"Media": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=eaumbmedia;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "umb-yunji-technology-ir"}}}}}