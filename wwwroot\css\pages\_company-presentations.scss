.company-presentations-layout {
    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;

        h2 {
            margin: 0;
            color: #333;
            font-size: 2.4rem;
            font-weight: 600;
        }

        #yearFilter {
            min-width: 150px;
            padding: 0.8rem 1.2rem;
            border: 1px solid #ddd;
            border-radius: 0.4rem;
            background: white;
            font-size: 1.4rem;
            color: #333;

            &:focus {
                outline: none;
                border-color: #1B73C0;
                box-shadow: 0 0 0 2px rgba(27, 115, 192, 0.1);
            }
        }
    }

    .timeline {
        .timeline-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: .8rem;
            background: #FFF;
            box-shadow: 0 0 2rem 0 rgba(0, 0, 0, 0.04);
            padding: 2rem;
            margin-bottom: 3rem;
            transition: transform .2s ease;
            &:last-child {
                margin-bottom: 0;
            }
            &:hover {
                transform: translateY(-2px);
                .timeline-date {
                    .day,
                    .month {
                        color: #1B73C0;
                    }
                }
                .timeline-content {
                    a {
                        color: #1B73C0;
                    }
                }
            }

            .timeline-date {
                display: flex;
                flex-direction: column;
                align-items: center;
                min-width: 6rem;
                
                .day,
                .month {
                    color: #333;
                }
                .day {
                    font-size: 3.2rem;
                    font-weight: 700;
                }
                .month {
                    font-size: 1.4rem;
                    font-weight: 400;
                    margin-top: .2rem;
                }
            }

            .timeline-content {
                flex: 1;
                margin: 0 2rem;
                white-space: nowrap;
                a {
                    font-size: 1.6rem;
                    color: #333;
                    font-weight: 400;
                }
            }
        }
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        margin-top: 4rem;

        button {
            padding: 1rem 2rem;
            border: 1px solid #ddd;
            border-radius: 0.4rem;
            background: white;
            color: #333;
            font-size: 1.4rem;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
                background: #1B73C0;
                color: white;
                border-color: #1B73C0;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }

        #pageInfo {
            font-size: 1.4rem;
            color: #666;
            font-weight: 500;
        }
    }
}