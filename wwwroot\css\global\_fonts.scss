@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Thin.otf') format('opentype');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-ExtraLight.otf') format('opentype');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Light.otf') format('opentype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Regular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-SemiBold.otf') format('opentype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plix';
    src: url('../../fonts/Plix/Plix-Text.otf') format('opentype');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}
