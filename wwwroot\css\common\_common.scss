html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  line-height: 1.5;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

a {
  text-decoration: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/******** Not edit in here *********/
html,
body {
  font-size: 16px;
  scroll-behavior: smooth;
  line-height: 1.5;
}

html[lang="cn"],
html[lang="hk"] {
  body {
    font-family:'Plix', Arial, sans-serif;
    font-weight: normal;
    overflow-x: hidden;
    color: $colorText;
  }
}

html[lang="en"] {
  body {
    font-family: Arial, sans-serif;
    font-weight: normal;
    overflow-x: hidden;
    color: $colorText;
  }
}

// CSS scrollbar
// html {
//   // scroll-padding-top: 13rem;
//   &::-webkit-scrollbar {
//     width: 0.8rem;
//   }

//   &::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 0.5rem grey;
//     box-shadow: inset 0 0 0.5rem grey;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: $color-primary;
//     border-radius: 1rem;
//   }

//   &::-webkit-scrollbar-thumb:hover {
//     background: #082b45;
//   }
// }

html {
  font-size: 54%;

  @media (min-width: 31.25rem) and (max-width: 61.938rem) {
    //991px
    font-size: 54%;
  }

  @media (min-width: 62rem) and (max-width: 64rem) {
    //1024px
    font-size: 55%;
  }

  @media (min-width: 65rem) and (max-width: 80rem) {
    //1280px
    font-size: 58%;
  }

  @media (min-width: 81rem) and (max-width: 90rem) {
    //1366px
    font-size: 60%;
  }

  @media (min-width: 91rem) {
    //1600px up
    font-size: 62.5%;
  }
}

.inner-content {
  padding: 0 0 10rem;
}

.container {
  padding: 0 15px;
  width: 100%;
  margin: 0 auto;
  z-index: 1;
  position: relative;

  @media (min-width: 48px) {
    //768px
    max-width: 100rem;
  }

  @media (min-width: 62rem) {
    //992px
    max-width: 110rem;
  }

  @media (min-width: 75rem) {
    //1200px
    max-width: 123rem;
  }

  // @media (min-width: 87.5rem) {
  //   //1400px
  //   max-width: 129rem;
  // }
  // @media (min-width: 93.75rem) {
  //   //1500px
  //   max-width: 140rem;
  // }
}

h2.page-title {
  color: #00201b;
  font-size: 4.5rem;
  font-style: normal;
  font-weight: 300;
  line-height: 1.37;
  @media screen and (max-width: 1440px) {
    font-size: 4.2rem;
  }
  @media screen and (max-width: 1280px) {
    font-size: 3.5rem;
  }
  @media screen and (max-width: 991px) {
    font-size: 3rem;
  }
}
img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}
