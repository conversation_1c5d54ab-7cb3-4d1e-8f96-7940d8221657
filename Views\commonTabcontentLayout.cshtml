﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models.PublishedContent;
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor;
@using Umbraco.Cms.Core.Models;
@{
	Layout = "childPageLayout.cshtml";
	var contents = Model.Children.Where(x => x.IsVisible());
	var tab = HttpContextAccessor.HttpContext?.Request.Query["tab"].ToString() ?? "";
}

<!-- Tab Header -->
<div>
	<div class="container-fluid">
		<div class="tab-header">
			<div class="page-title">@Model.Value("pageTitle")</div>
			<div id="tab-header"
				class="tab-head">
				@foreach (var item in contents)
				{
					var tabName = item.Name.ToLower().Replace(" ", "-").Replace("'", "-");
					var isActive = tabName == tab || (string.IsNullOrEmpty(tab) && contents.First() == item);
					var documentType = item.ContentType.Alias;
					bool isRerirect = false;
					var redirectLink = "";
					if (documentType.Equals("redirectPage"))
					{
						isRerirect = true;
						redirectLink = item.Value<Link>("redirectLink").Url;
					}
					<div id="@tabName-header" class="nav-item tab-button @(item==contents.First() ? "active":"") @(isRerirect ? "redirect-page" : "")"
						data-tab="@tabName" data-redirect="@redirectLink">
						<div class="nav-link @(item==contents.First() ? "active":"")">@item.Value("pageTitle")</div>
					</div>
				}
			</div>
		</div>		
	</div>

	<!-- Tab Content -->
	@if (contents != null && contents.Any())
	{
		int iIndex = 0;
		<div class="tab-content">

			@foreach (var item in contents)
			{
				var tabName = item.Name.ToLower().Replace(" ", "-").Replace("'", "-");
				var documentType = item.ContentType.Alias;
				var toolSource = "";
				var dataToolName = "";
				var dataToolVersion = "";
				var dataToolCompany = "";
				var dataToolUrlBase = "";
				var dataLazy = "";
				var dataToolEnforcedCompanyCode = "";
				var dataToolLanguage = "";
				var dataToolAbsoluteUrl = "";
				if (documentType.Equals("commonEmbedPage"))
				{
					toolSource = item.Value<string>("toolSource");
					dataToolName = item.Value<string>("dataToolName");
					dataToolVersion = item.Value<string>("dataToolVersion");
					dataToolCompany = item.Value<string>("dataToolCompany");
					dataToolUrlBase = item.Value<string>("dataToolUrlBase");
					dataLazy = item.Value<string>("dataLazy");
					dataToolEnforcedCompanyCode = item.Value<string>("dataToolEnforcedCompanyCode");
					dataToolLanguage = item.Value<string>("dataToolLanguage");
					dataToolAbsoluteUrl = item.Value<string>("dataToolAbsoluteUrl");
				}

				<div id="@tabName-content" class="tab-pane @(item==contents.First() ? "active in show":"")">
					@if (documentType.Equals("commonEmbedPage"))
					{
						<div class="container-fluid">
							@if (dataToolName.Equals("Bundle"))
							{
								<euroland-tool data-tool-name="@dataToolName" data-tool-version="@dataToolVersion"
									data-tool-absolute-url="@dataToolAbsoluteUrl" data-tool-language="@dataToolLanguage"
									data-tool-company="@dataToolCompany">
								</euroland-tool>
							}
							else
							{
								<euroland-tool data-tool-name="@dataToolName" data-tool-company="@dataToolCompany"
									data-tool-url-base="@dataToolUrlBase" data-tool-language="@dataToolLanguage"
									data-tool-version="@dataToolVersion">
								</euroland-tool>
							}
						</div>
					}
					@if (documentType.Equals("listingDocumentsPage"))
					{
						<partial name="Information Disclosure/Listing Documents" />
					}
					
				</div>
				iIndex++;
			}
		</div>
	}
</div>
