﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	Layout = "childPageLayout.cshtml";
}
<style>
    .internal-server-error-page {
        text-align: center;
        font-family: Helvetica, sans-serif;
    }

    .internal-server-error-page .error-section {
        padding: 40px 20px;
    }

    .internal-server-error-page .error-section h1 {
        font-size: 150px;
        color: #E8380D;
        text-shadow: 1px 1px 1px #ddd, 2px 2px 1px #ddd,
            3px 3px 1px #ddd, 4px 4px 1px #ddd, 5px 5px 1px #ddd,
            6px 6px 1px #ddd, 7px 7px 1px #ddd, 8px 8px 1px #ddd,
            25px 25px 8px rgba(0, 0, 0, 0.2);
        margin: 0;
    }

    .internal-server-error-page .error-section h3 {
        margin: 20px 0;
        font-size: 30px;
        font-weight: 600;
        color: #000;
    }

    .internal-server-error-page .error-section .error-content {
        color: #000;
        font-size: 18px;
        line-height: 1.5;
        font-weight: normal;
    }
</style>
<div class="error-page container">    
    <div class="internal-server-error-page">
        <div class="error-section">
            <h1 class="error-title page-title">500</h1>
            <h3>Internal Server Error</h3>
            <div class="error-content">
                <p>Unfortunately, something has gone wrong.</p>
                <p>
                    We're unable to fulfill your request. Rest assured we have been
                    notified and are looking into the issue. <br />Please refresh your
                    browser. If the error continues, please contact our support team.
                </p>
            </div>
        </div>
    </div>
</div>