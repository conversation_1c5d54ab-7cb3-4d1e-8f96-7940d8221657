$(document).ready(function () {
  // Scroll handler
  function handleHeaderScroll() {
    if ($(window).scrollTop() > 130) {
      $(".site-header").addClass("header-fixed");
    } else {
      $(".site-header").removeClass("header-fixed");
    }
  }

  $(window).on("scroll", handleHeaderScroll);

  function setupMenuItemToggle() {
    $(".site-header")
      .off("click.menuToggle")
      .on(
        "click.menuToggle",
        ".menu-item.hassub > .menu-item-footer > a, .menu-item.hassub > .menu-item-footer > .open-sub",
        function (e) {
          const $menuItem = $(this).closest(".menu-item.hassub");
          const $submenu = $menuItem
            .find(
              "> .menu-item-footer > ul.menu-sub-lv2, > .menu-item-footer > ul.menu-sub-lv3"
            )
            .first();

          if (!$submenu.length) return;

          e.preventDefault();

          const isHidden = $submenu.hasClass("hidden");
          if (isHidden) {
            $menuItem
              .siblings(".menu-item.hassub")
              .removeClass("open")
              .addClass("close")
              .find("> .menu-item-footer > ul")
              .addClass("hidden")
              .slideUp(300);

            // Mở submenu hiện tại
            $submenu.removeClass("hidden").stop(true, true).slideDown(300);
            $menuItem.removeClass("close").addClass("open");
          } else {
            // Đóng submenu hiện tại
            $submenu.addClass("hidden").stop(true, true).slideUp(300);
            $menuItem.removeClass("open").addClass("close");
          }
        }
      );
  }

  function setupOffCanvasMenu() {
    const $body = $("body");
    const $headerMiddle = $(".header-middle");
    const $toggleBtnMenuOffcanvas = $(".btn-menu-offcanvas");

    $toggleBtnMenuOffcanvas.on("click", function (e) {
      e.preventDefault();

      if ($headerMiddle.hasClass("open")) {
        $headerMiddle.removeClass("open");
        $toggleBtnMenuOffcanvas.removeClass("active-menu");
        $body.removeClass("offcanvas-open");
        setTimeout(() => $headerMiddle.hide(), 500);
      } else {
        $headerMiddle.show();
        setTimeout(() => {
          $headerMiddle.addClass("open");
          $toggleBtnMenuOffcanvas.addClass("active-menu");
          $body.addClass("offcanvas-open");
        }, 10);
      }
    });

    $(document).on("click", function (e) {
      if (
        $headerMiddle.hasClass("open") &&
        !$headerMiddle.is(e.target) &&
        $headerMiddle.has(e.target).length === 0 &&
        !$toggleBtnMenuOffcanvas.is(e.target) &&
        $toggleBtnMenuOffcanvas.has(e.target).length === 0
      ) {
        $headerMiddle.removeClass("open");
        $toggleBtnMenuOffcanvas.removeClass("active-menu");
        $body.removeClass("offcanvas-open");
        setTimeout(() => $headerMiddle.hide(), 500);
      }
    });
  }

  function syncLangLinksWithHash() {
    var hash = window.location.hash;
    if (!hash) return;
    document
      .querySelectorAll(".language .lang-status a[href]")
      .forEach(function (a) {
        try {
          var url = new URL(a.getAttribute("href"), window.location.origin);
          url.hash = hash;
          a.setAttribute("href", url.toString());
        } catch (_) {}
      });
  }
  $(document).on("click", ".language .lang-status a[href]", function () {
    syncLangLinksWithHash();
  });
  window.addEventListener("hashchange", syncLangLinksWithHash);

  handleHeaderScroll();
  setupOffCanvasMenu();
  setupMenuItemToggle();
  syncLangLinksWithHash();
});
