.site-footer {
  .footer-wrapper {
  }
  .container .sp-bottom {
    @media screen and (min-width: 1200px) {
      display: none;
    }
  }
  .container .pc-bottom {
    display: none;
    @media screen and (min-width: 1200px) {
      display: flex;
      flex-wrap: wrap;
      margin-top: 6.5rem;
    }
  }
  .footer-bottom {
    margin-bottom: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .line {
      color: #b9b9b9;
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding: 0 3px 0 4px;
    }
    .footer-bottom-copyright {
      color: #b9b9b9;
      font-size: 1.5rem;
      font-style: normal;
      font-weight: 300;
      line-height: normal;
    }
    .footer-bottom-icp,
    .footer-bottom-quick-links {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      a {
        color: #b9b9b9;
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
        transition: all 0.3s linear;
        &:hover {
          // color: $color-primary;
          background: linear-gradient(90deg, #d71820 0%, #710d11 86.5%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .footer-bottom-quick-links {
      .line:last-child {
        display: none;
      }
    }
    @media screen and (max-width: 1199px) {
      flex-wrap: wrap;
    }
    @media screen and (max-width: 991px) {
      justify-content: flex-start;
    }
  }
  .footer-inner {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 6rem 0;
    position: relative;
    gap: 3rem;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, #d71820 0%, #710d11 100%);
    }
    .footer-wrap {
      .footer-logo {
        margin-bottom: 2.2rem;
        .logo {
          img {
            max-width: 35.3rem;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
      .footer-contact {
        display: flex;
        align-items: center;
        @media screen and (min-width: 1024px) {
          justify-content: end;
        }
        .footer-wechat {
          position: relative;
          flex: 1;
          .icon {
            margin-left: 0.7rem;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            img {
              height: 100%;
            }
          }
          .qr-code {
            display: none;
            position: absolute;
            bottom: 50px;
            left: -50px;
            width: 150px;
            height: 150px;
            box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.35);
            transition: all 0.3s linear;
            z-index: 1;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          &:hover {
            .qr-code {
              display: block;
            }
          }
        }
        .footer-wechat.close {
          .qr-code {
            display: block;
          }
        }
        .foote-mail {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            padding: 9px 25px;
            border-radius: 10px 0 0 10px;
            border: 0.3px solid #737373;
            background: #fff;
            color: #737373;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 1;
            // height: 34px;
            border-right: 0;
          }
          .btn-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72px;
            height: 36px;
            border-radius: 0 10px 10px 0;
            background: linear-gradient(180deg, #d71820 0%, #710d11 100%);
          }
        }
      }
    }
    .footer-right {
      .footer-menu {
        .nav-container {
          display: flex;
          align-items: flex-start;
          justify-content: flex-end;
          gap: 2.5rem;
          &:lang(cn),
          &:lang(hk) {
            @media screen and (min-width: 1024px) {
              gap: 3.5rem;
            }
          }
          .menu-item.level-2 {
            display: block;
            > a {
              display: block;
            }
            a {
              flex-shrink: 0;
              color: #000000;
              font-size: 1.8rem;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
            }
            .open-sub {
              flex-shrink: 0;
              display: inline-block;
              opacity: 1;
              visibility: visible;
              cursor: pointer;
              width: 20px;
              height: 20px;
              background: url("/media/vf5dcvfb/arrow-icon.png") no-repeat center
                center / cover;
              top: 3px;
              position: relative;
            }
            .menu-sub-lv2 {
              width: 100%;
              display: flex;
              align-items: flex-start;
              justify-content: flex-start;
              flex-direction: column;
              overflow: hidden;
              transition: all 0.3s linear;
              display: none;
              margin-top: 1rem;
            }
          }
          .menu-item.level-2.open {
            > a {
              background: linear-gradient(90deg, #d71820 0%, #710d11 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .open-sub {
              rotate: 180deg;
              transition-property: all;
              transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
              transition-duration: 300ms;
              // top: 10px;
              // position: relative;
            }
            .menu-sub-lv2 {
              display: block;
              height: 160px;
            }
          }

          .menu-item.level-3 {
            &:not(:last-child) {
              margin-bottom: 1rem;
            }
            a {
              color: #737373;
              font-size: 1.6rem;
              &:hover {
                // color: $color-primary;
                background: linear-gradient(90deg, #d71820 0%, #710d11 86.5%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
          }
        }
      }
    }
    @media screen and (max-width: 1499px) {
      .footer-wrap {
        max-width: 36rem;
        .footer-contact {
          .footer-wechat {
            .icon {
              margin-left: 0;
            }
          }
          .foote-mail {
            .label {
              padding: 6px 20px 7px 20px;
            }
          }
        }
      }
      .footer-right {
        .footer-menu {
          .nav-container {
            gap: 2rem;
            .menu-item.level-2 {
              a {
                font-size: 1.6rem;
              }
            }
            .menu-item.level-2.open {
              .menu-sub-lv2 {
                // height: 130px;
              }
            }
          }
        }
      }
    }

    @media screen and (max-width: 1199px) {
      .footer-right {
        .footer-menu {
          .nav-container {
            flex-wrap: wrap;
            .menu-item.level-2 {
              width: calc((100% / 3) - 6rem);
            }
          }
        }
      }
    }
    @media screen and (max-width: 1039px) {
      .footer-wrap {
        max-width: 36rem;
        .footer-contact {
          .footer-wechat {
            .icon {
              margin-left: 0;
            }
          }
          .foote-mail {
            .label {
              padding: 6px 15px 7px 15px;
            }
            .btn-link {
              width: 55px;
            }
          }
        }
      }
    }
    @media screen and (max-width: 991px) {
      flex-direction: column;
      padding: 4rem 0;
      .footer-wrap {
        max-width: 100%;
        margin-bottom: 2.6rem;
        width: 100%;
      }
      .footer-right {
        width: 100%;
        .footer-menu {
          .nav-container {
            justify-content: flex-start;
            gap: 1rem;
            .menu-item.level-2 {
              width: calc(50% - 1rem);
              > a {
                margin-bottom: 1.5rem;
              }
            }
          }
        }
      }
    }
    // @media screen and (min-width: 1200px) {
    //   flex-direction: row;
    // }
  }
}
.menu-item-footer {
  @media screen and (min-width: 1024x) {
    &:hover {
      .menu-responsive {
        display: block !important;
        height: 160px;
      }
    }
  }
}
