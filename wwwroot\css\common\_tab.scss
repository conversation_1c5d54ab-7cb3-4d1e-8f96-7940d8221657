.tab-head {
  display: flex;
  align-items: center;
  gap: 5rem;
  margin-bottom: 6.6rem;
  background: #f5f5f5;
  overflow: auto;
  height: 6rem;
  @media screen and (min-width: 1280px) {
    justify-content: center;
  }
  .nav-item {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 300;
    line-height: 20px;
    padding: 14px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    height: 6rem;
    display: flex;
    align-items: center;
    &:hover {
      font-weight: 500;
    }
    &.active {
      font-weight: 500;
      ::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 12rem;
        height: 2px;
        background: linear-gradient(
          93deg,
          #740101 9.54%,
          #f40100 50%,
          #8d0201 90.46%
        );
      }
    }
  }
}
.tabs__wrapper {
  display: flex;
  align-items: center;
  gap: 5rem;
  margin-bottom: 6.6rem;
  background: #f5f5f5;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
  // &::-webkit-scrollbar-track {
  //   -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  //   border-radius: 10px;
  //   background-color: #f5f5f5;
  // }
  // &::-webkit-scrollbar {
  //   width: 12px;
  //   height: 3px;
  //   background-color: #f5f5f5;
  // }
  // &::-webkit-scrollbar-thumb {
  //   border-radius: 10px;
  //   -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  //   background: linear-gradient(90deg, #d71820 10%, #710d11 90%);
  // }
  @media (max-width: 578px) {
    gap: 1rem;
  }
  @media screen and (min-width: 1024px) {
    justify-content: center;
  }
  .tabs-link {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 300;
    line-height: 20px;
    padding: 14px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    transition: all 0.3s ease-in-out;
    ::after {
      content: "";
      position: absolute;
      left: 50%;
      width: 0;
      bottom: 0;
      opacity: 0;
      transform: translateX(-50%);
      height: 2px;
      background: linear-gradient(
        93deg,
        #740101 9.54%,
        #f40100 50%,
        #8d0201 90.46%
      );
    }
    &.active,
    &:hover {
      font-weight: 500;
      ::after {
        opacity: 1;
        width: 12rem;
        transition: all 0.5s ease-in-out;
      }
    }
    .nav-link {
      font-size: 16px;
      @media screen and (min-width: 1366px) {
        font-size: 18px;
      }
    }
  }
}
