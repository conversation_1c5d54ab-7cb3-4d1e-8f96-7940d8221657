{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>"}]}}]}, "Umbraco": {"CMS": {"Unattended": {"InstallUnattended": true}, "Content": {"MacroErrors": "<PERSON>hrow"}, "Hosting": {"Debug": true}, "Security": {"BackOfficeHost": "https://localhost:44327/"}, "WebRouting": {"UmbracoApplicationUrl": "https://localhost:44327/"}}}}