.sitemap-container {
	ul {
		li {
			a {
				color: #0a2333;
			}
		}
	}
	.level-2 {
		margin-bottom: 1rem;
		> a {
			> span {
				font-weight: bold;
				color: #0a2333;
			}
		}
		> ul {
			padding: 0 2rem;
		}
	}
	.level-3 {
		> ul {
			padding: 0 2rem;
		}
		padding: 0.5rem 0;
	}
	.page-title {
		text-align: left;
		margin-bottom: 55rem;
		position: relative;
		z-index: 2;
		font-weight: 700;
		font-size: 3.6rem;
		&:after {
			content: "";
			position: absolute;
			bottom: -0.5rem;
			left: 0;
			width: 15rem;
			height: 0.4rem;
			z-index: -1;
		}
	}
}

.sitemap,
.template-siteMapPage {
	.inner-content {
		min-height: 100vh;
	}
}
