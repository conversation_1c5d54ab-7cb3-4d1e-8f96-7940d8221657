﻿$(document).ready(function () {
  $("select").niceSelect();

  function setupToggle(container, itemClass, tableClass) {
    $(container).on("click", itemClass, function () {
      var target = $($(this).data("target"));

      $(container)
        .find(tableClass)
        .not(target)
        .slideUp(300)
        .prev(itemClass)
        .removeClass("active");

      target.slideToggle(300);

      $(this).toggleClass("active");
    });
  }

  setupToggle(
    ".statement-section__list",
    ".statement__body",
    ".statement__answer"
  );
  setupToggle(
    ".news_section_list",
    ".notices__list__header",
    ".notices__list__text"
  );

  document.getElementById("news-detail-back")?.addEventListener("click", () => {
    window.history.back();
  });

  $(".breadcrumb__item").first().addClass("active");

  $(".breadcrumb__item").each(function () {
    var currentPath = window.location.pathname;
    var breadcrumbPath = $(this).attr("href");

    if (breadcrumbPath === currentPath) {
      $(".breadcrumb__item").removeClass("active");
      $(this).addClass("active");
    }
  });

  $(".breadcrumb__item").on("click", function () {
    $(".breadcrumb__item").removeClass("active");
    $(this).addClass("active");
  });

  $(".popup-bod").on("click", function () {
    var popupId = $(this).closest(".popup-bod").data("popup");
    var $popup = $("#" + popupId);
    $popup.fadeIn();
    $(".background-opacity").addClass("active");
  });

  $(".btn-bod-close").on("click", function () {
    $(this).closest(".popup-overlay-bod").fadeOut();
    $(".background-opacity").removeClass("active");
  });

  var $root = $(".map-content");

  $root.find(".popup-map-item").hide();

  function openPopup($popup) {
    // Ẩn mọi popup khác trước khi mở cái mới
    $root
      .find(".popup-map-item:visible")
      .not($popup)
      .stop(true, true)
      .fadeOut(150);
    $popup.stop(true, true).fadeIn(150);
  }

  function closePopup($popup) {
    $popup.stop(true, true).fadeOut(150);
  }

  $root.on("click", ".map-pin > img", function (e) {
    e.preventDefault();
    e.stopPropagation();
    var $pin = $(this).closest(".map-pin");
    var $popup = $pin.children(".popup-map-item");
    if ($popup.length) openPopup($popup);
  });

  $root.on("click", ".btn-map-close", function (e) {
    e.preventDefault();
    e.stopPropagation();
    closePopup($(this).closest(".popup-map-item"));
  });

  $(document).on("click", function (e) {
    if ($(e.target).closest(".popup-map-item, .map-pin > img").length === 0) {
      $root.find(".popup-map-item:visible").stop(true, true).fadeOut(150);
    }
  });

  $(document).on("keyup", function (e) {
    if (e.key === "Escape") {
      $root.find(".popup-map-item:visible").stop(true, true).fadeOut(150);
    }
  });

  var currentUrl = window.location.pathname;

  $(".tabs__wrapper .tabs-link").each(function () {
    var $link = $(this).find("a.nav-link");
    if ($link.attr("href") === currentUrl) {
      $(this).addClass("active");
    } else {
      $(this).removeClass("active");
    }
  });

  var $activeTab = $(".tabs__wrapper .tabs-link.active");
  if ($activeTab.length) {
    var $wrapper = $(".tabs__wrapper");
    var wrapperWidth = $wrapper.width();
    var activeTabWidth = $activeTab.outerWidth();
    var activeTabOffset = $activeTab.position().left;
    var scrollPosition =
      activeTabOffset - wrapperWidth / 2 + activeTabWidth / 2;
    $wrapper.scrollLeft(scrollPosition);
  }
  setTimeout(function () {
    AOS.init();
  }, 1000);
  (function () {
    const OVERALL_TIMEOUT_MS = 12000; // hard stop: never block forever
    const VIDEO_TIMEOUT_MS = 4000; // mobile Safari often blocks autoplay preload
    const preloaderEl = document.getElementById("preloader");
    const barEl = document.getElementById("preloaderBar");
    const pctEl = document.getElementById("preloaderPct");
    let completed = 0;

    // Helper to update progress UI
    function setProgress(fraction) {
      const pct = Math.max(0, Math.min(100, Math.round(fraction * 100)));
      barEl.style.width = pct + "%";
      pctEl.textContent = pct;
    }

    // Wrap a promise to increment progress when it settles
    function track(promise) {
      return promise.finally(() => {
        completed++;
        setProgress(completed / total);
      });
    }

    // Wait for all <img>
    function waitImage(img) {
      if (img.complete) return Promise.resolve();
      return new Promise((resolve) => {
        img.addEventListener("load", resolve, { once: true });
        img.addEventListener("error", resolve, { once: true }); // count errors as "done" so we don't block
      });
    }

    // Wait for <video> to be reasonably ready
    function waitVideo(v) {
      // readyState >= 3 (HAVE_FUTURE_DATA) is ok for autoplay muted loops
      if (v.readyState >= 3) return Promise.resolve();
      return new Promise((resolve) => {
        const done = () => {
          cleanup();
          resolve();
        };
        const cleanup = () => {
          v.removeEventListener("canplaythrough", done);
          v.removeEventListener("loadeddata", done);
          v.removeEventListener("error", done);
        };
        v.addEventListener("canplaythrough", done, { once: true });
        v.addEventListener("loadeddata", done, { once: true });
        v.addEventListener("error", done, { once: true });
        // Safety net for iOS Safari not preloading video
        setTimeout(done, VIDEO_TIMEOUT_MS);
      });
    }

    // Wait for document fonts (if supported)
    const fontsReady =
      "fonts" in document && document.fonts && document.fonts.ready
        ? document.fonts.ready.catch(() => {}) // treat failure as done
        : Promise.resolve();

    // Collect assets that are present in DOM on first paint
    const images = Array.from(document.images);
    const videos = Array.from(document.querySelectorAll("video"));

    // Build promise list
    const promises = [
      ...images.map(waitImage),
      ...videos.map(waitVideo),
      fontsReady,
    ];

    // Count total units for progress (avoid 0)
    const total = Math.max(1, promises.length);

    // Start with 5% to feel responsive
    setProgress(0.05);

    // Lock scroll & show overlay ASAP
    document.documentElement.classList.add("is-preloading");
    document.body.classList.add("preloading");

    const allAssets = Promise.all(promises.map(track));

    // Also wait for window 'load' (stylesheets, etc.)
    const windowLoaded = new Promise((resolve) => {
      if (document.readyState === "complete") return resolve();
      window.addEventListener("load", resolve, { once: true });
    });

    // Race with an overall timeout so we never hang
    const hardStop = new Promise((resolve) =>
      setTimeout(resolve, OVERALL_TIMEOUT_MS)
    );

    Promise.race([Promise.all([allAssets, windowLoaded]), hardStop]).then(
      () => {
        setProgress(1);
        // Let the bar reach 100% visually before removing
        setTimeout(() => {
          preloaderEl.classList.add("done");
          document.body.classList.remove("preloading");
          document.documentElement.classList.remove("is-preloading");
          // Optional: remove node after fade to keep DOM clean
          setTimeout(() => preloaderEl.remove(), 400);
        }, 150);
      }
    );
  })();
});
