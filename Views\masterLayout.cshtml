﻿@using Microsoft.Extensions.Options
@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Microsoft.Extensions.Options

@{
    var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
    var viewName = Model.Name.ToLower().Replace(" ", "-").Replace("'", "-");
    var home = Model.Root();
    if (language.Equals("en-US"))
    {
        language = "en";
    }
    if (language.Equals("zh-Hans") || language.Equals("zh-Hans-CN"))
    {
        language = "cn";
    }
    if (language.Equals("zh-Hant") || language.Equals("zh-Hant-HK"))
    {
        language = "hk";
    }
    var dir = language.Equals("ar-SA") ? "dir=rtl" : "dir=ltr";
    var keywordsArray = Model.Value<string[]>("keywords") ?? Array.Empty<string>();
    var keywords = string.Join(", ", keywordsArray);
    if (string.IsNullOrEmpty(keywords))
    {
        var homeKeywords = home.Value<string[]>("keywords") ?? Array.Empty<string>();
        keywords = string.Join(", ", homeKeywords);
    }
    var descriptions = "";
    if (Model.HasValue("seoMetaDescription") && Model.Value("seoMetaDescription") != null)
    {
        descriptions = Model.Value<string>("seoMetaDescription");
    }
    if (string.IsNullOrEmpty(descriptions))
    {
        descriptions = home.Value<string>("seoMetaDescription");
    }
    var modelName = Model.Name.ToLower().Replace(" ", "-").Replace("'", "-").Replace(",", "");
}

<!doctype html>
<!--[if gt IE 8]><!-->
<html class="no-js @Model.Name.ToLower().Replace(" ", "-").Replace("'", "-").Replace(",", "") group" lang="@language"
    @dir>
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <link rel="stylesheet" href="/bundles/inline-css-bundle.css" />
    <meta name="description" content="@descriptions">
    <meta name="keywords" content="@keywords">
    <meta name="msvalidate.01" content="">
    <meta name="robots" content="noindex">
    <title>@(Model.Value<string>("pageTitle") + (" | ") + home.Value<String>("companyName"))</title>
    <!-- Google tag (gtag.js) -->
    <script>
        (function (w, d, s, e, l, i) {
            w[s] = w[s] || []; w[s].push({ 'company': e }); w[s].push({ 'reg_key': l });
            w[s].push({ 'svc_url': i }); var f = d.getElementsByTagName('script')[0],
                j = d.createElement('script'); j.async = true; j.src = i + '/integration/latest/embed.js';
            j.id = 'euroland-integration'; !d.getElementById(j.id) && f.parentNode.insertBefore(j, f);
        })
            (window, document, 'eurolandClientCfg', 'companyCode', null, "https://asia.tools.euroland.com/tools/");
    </script>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-123456"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-123456');
    </script>
</head>

<body class="@Model.Name.ToLower().Replace(" ", "-").Replace("'", "-").Replace(",", "") @language">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=G-123456" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <div class="background-opacity"></div>
    <div class="warning-search">@Umbraco.GetDictionaryValue("Enter search content!")</div>
    @if (modelName != "error-page")
    {
        <header class="site-header">
            @* <partial name="Layout/Header" />  *@
        </header>

        <section class="homepage-banner banner">
            @* <partial name="Layout/Banner" /> *@
        </section>
    }
    <main class="site-content">
        @RenderBody()
    </main>
    @if (modelName != "error-page")
    {
        <footer class="site-footer">
           @* <partial name="Layout/Footer" /> *@
        </footer>
    }
    <script src="/bundles/inline-js-bundle"></script>
</body>

</html>