.home-banner.banner {
  .swiper.mySwiper {
    .swiper-wrapper {
      .swiper-slide {
        min-height: 82rem;
        max-height: 82rem;
        .wrap-content {
          .title-banner {
            display: none;
          }
        }
        &:nth-child(2),
        &:nth-child(3) {
          .wrap-content {
            .content-description {
              color: #231f20;
            }
          }
        }
      }
    }
  }
  @media screen and (max-width: 1499px) {
    .swiper.mySwiper {
      .swiper-wrapper {
        .swiper-slide {
          min-height: 60rem;
          max-height: 60rem;
          .wrap-content {
            margin-top: 10rem;
          }
        }
      }
    }
  }
  @media screen and (max-width: 991px) {
    .swiper.mySwiper {
      .swiper-wrapper {
        .swiper-slide {
          min-height: 50rem;
          max-height: 50rem;
          .wrap-content {
            margin-top: 4rem;
          }
        }
      }
    }
  }
}
.banner {
  margin-top: 80px;
  @media screen and (min-width: 992px) {
    margin-top: 100px;
  }
  .swiper.mySwiper {
    .swiper-wrapper {
      .swiper-slide {
        min-height: 60rem;
        max-height: 60rem;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 100% 100%;
        .wrap-content {
          margin-top: 24rem;
          .title-banner {
            color: #fff;
            text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
            // font-size: 10rem;
            // font-size: clamp(50px, 5vw, 80px);
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            font-size: 40px;
            @media (min-width: 768px) {
              font-size: 48px;
            }
            @media (min-width: 1024px) {
              font-size: 56px;
            }
            @media (min-width: 1366px) {
              font-size: 64px;
            }
            @media (min-width: 1441px) {
              font-size: 72px;
            }
            @media (min-width: 1661px) {
              font-size: 80px;
            }
          }
          .content-description {
            color: #f5f5f5;
            // font-size: clamp(32px, 5vw, 57.6px);
            font-style: normal;
            font-weight: 500;
            line-height: 1.3;
            font-size: 32px;
            @media (min-width: 768px) {
              font-size: 37px;
            }
            @media (min-width: 1024px) {
              font-size: 42px;
            }
            @media (min-width: 1366px) {
              font-size: 47px;
            }
            @media (min-width: 1441px) {
              font-size: 52px;
            }
            @media (min-width: 1661px) {
              font-size: 57.6px;
            }
          }
        }
      }
    }

    .ticker-tool-banner {
      position: absolute;
      bottom: 6.5rem;
      left: 0;
      z-index: 10;
      width: 67%;
      height: 140px;
      border-radius: 0 5px 5px 0;
    }

    .swiper-pagination {
      .swiper-pagination-bullet {
        width: 6px;
        height: 6px;
        background: #d9d9d9;
        transition: all 0.3s ease-in-out;
        opacity: 1;
      }
      .swiper-pagination-bullet-active {
        width: 30px;
        border-radius: 10px;
        background: linear-gradient(90deg, #d71820 0.17%, #710d11 99.83%);
      }
    }
  }
  @media screen and (max-width: 1499px) {
    .swiper.mySwiper {
      .swiper-wrapper {
        .swiper-slide {
          background-position: 0 100%;
        }
      }
    }
  }
  @media screen and (max-width: 1199px) {
    .swiper.mySwiper {
      .ticker-tool-banner {
        width: 100%;
        right: 0;
        border-radius: 0;
        bottom: 2.5rem;
      }
    }
  }
  @media screen and (max-width: 991px) {
    .swiper.mySwiper {
      .swiper-wrapper {
        .swiper-slide {
          min-height: 45rem;
          max-height: 45rem;
          .wrap-content {
            margin-top: 16rem;
          }
        }
      }
    }
  }
  @media screen and (max-width: 768px) {
    .swiper.mySwiper {
      .swiper-wrapper {
        .swiper-slide {
          min-height: 35rem;
          max-height: 35rem;
          .wrap-content {
            margin-top: 12rem;
          }
        }
      }
    }
  }
}
