$(document).ready(function () {
  const buttons = document.querySelectorAll(".tab-button");
  const tabPanes = document.querySelectorAll(".tab-pane");
  const activeButtonClass = ["active"];
  const activeNavLink = ["active"];

  if (buttons.length === 0 || tabPanes.length === 0) return;

  function showTab(tab, pushState = true) {
    const tabHeader = document.getElementById(`${tab}-header`);
    const tabContent = document.getElementById(`${tab}-content`);

    if (!tabHeader || !tabContent) return;

    if (pushState) history.pushState(null, null, `#${tab}`);

    buttons.forEach((button) => {
      button.classList.remove(...activeButtonClass);
      button.classList.remove(...activeNavLink);
    });

    tabHeader.classList.add(...activeButtonClass);
    tabHeader.classList.add(...activeNavLink);
    tabPanes.forEach((pane) => {
      pane.classList.add("hidden");
      pane.classList.remove("active", "in", "show");
    });

    tabContent.classList.remove("hidden");
    tabContent.classList.add("active", "in", "show");

    const iframe = tabContent.querySelector("iframe.eurolandtool");
    if (
      iframe &&
      iframe.hasAttribute("data-src") &&
      !iframe.getAttribute("src")
    ) {
      iframe.setAttribute("src", iframe.getAttribute("data-src"));
    }
  }

  // Click event
  buttons.forEach((button) => {
    const tab = button.getAttribute("data-tab");
    button.addEventListener("click", function (e) {
      if (button.classList.contains("redirect-page")) return;
      showTab(tab);
    });
  });

  function initTabFromHash() {
    const hash = window.location.hash.substring(1); 
    const availableTabs = [...buttons].map((btn) =>
      btn.getAttribute("data-tab")
    );
    const defaultTab = buttons[0]?.getAttribute("data-tab");

    const tabToShow = availableTabs.includes(hash) ? hash : defaultTab;
    showTab(tabToShow, false);
  }

  window.addEventListener("popstate", function () {
    initTabFromHash();
  });

  $("#tab-header .redirect-page").click(function (e) {
    const redirectLink = $(this).attr("data-redirect");
    if (redirectLink) window.location.href = redirectLink;
  });

  initTabFromHash();
});
