function LoadBannerImages(e) {
  var slideItem = $(".banner-wrap .swiper-wrapper .swiper-slide"),
    dragSlider = slideItem.attr("data-drag-slider"),
    effectSlider = slideItem.attr("data-effect-slider"),
    timingEachSlide = slideItem.attr("data-timing"),
    dataSwiper =
      '{"dragSlider":"' +
      dragSlider +
      '",' +
      '"effectSlider":"' +
      effectSlider +
      '",' +
      '"timingEachSlide":"' +
      timingEachSlide +
      '"}',
    dataAttr = JSON.parse(dataSwiper);

  //there are more one item we will add the swiper
  if (slideItem && slideItem.length > 1) {
    var swiper = new Swiper(".banner-swiper", {
      slidesPerView: 1,
      spaceBetween: 0,
      speed: 1000,
      pagination: {
        el: ".swiper-pagination",
      },
      allowTouchMove: "true" == dataAttr.dragSlider,
      autoplay: {
        delay: dataAttr.timingEachSlide,
      },
      loop: true,
      observer: true,
      observeParents: true,
      on: {
        init() {
          initTickerToolIn(this.slides[this.activeIndex]);
        },
        slideChange() {
          initTickerToolIn(this.slides[this.activeIndex]);
        },
      },
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      effect: dataAttr.effectSlider,
      flipEffect: {
        slideShadows: true,
      },
      coverflowEffect: {
        rotate: 30,
        slideShadows: true,
      },
      cubeEffect: {
        slideShadows: true,
      },
      creativeEffect: {
        prev: {
          translate: [0, 0, -400],
        },
        next: {
          translate: ["100%", 0, 0],
        },
      },
    });
  }
}
function initTickerToolIn(slideEl) {
  const tool = slideEl?.querySelector(".ticker-tool-banner euroland-tool");
  if (!tool || tool.dataset.initialized === "1") return;
  tool.dataset.initialized = "1";
}
function popupBanner() {
  $(".btn-video-popup").magnificPopup({
    disableOn: 700,
    type: "iframe",
    mainClass: "mfp-fade",
    preloader: false,

    fixedContentPos: false,
  });
}

function ResizeBanner(maxWidth) {
  $window = $(window);
  if ($window.width() <= maxWidth) {
    var $width = $window.width();
    var numberScale = 2.57;
    if ($(".childpage-content").hasClass("investor-relations")) {
      numberScale = 2.02;
    }

    $heightBanner = $width / numberScale;
    $(".swiper-slide").css("height", $heightBanner);
  } else {
    $(".swiper-slide").css("height", "");
  }
}

function BannerHeight(maxWidth) {
  ResizeBanner(maxWidth);
  $window.resize(function () {
    $window = $(window);
    if ($window.width() <= maxWidth) {
      ResizeBanner(maxWidth);
    } else {
      $(".swiper-slide").css("height", "");
    }
  });
}

$(document).ready(function () {
  LoadBannerImages();
  popupBanner();
  BannerHeight(1025);
});
